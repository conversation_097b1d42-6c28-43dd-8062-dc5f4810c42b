<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class QuantityDiscount extends Model
{
    use HasFactory;

    protected $fillable = [
        'product_id',
        'min_quantity',
        'max_quantity',
        'discount_type',
        'discount_value',
        'is_active',
        'starts_at',
        'expires_at'
    ];

    protected $casts = [
        'discount_value' => 'decimal:2',
        'is_active' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the product that owns the quantity discount
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Check if discount is valid
     */
    public function isValid(): bool
    {
        // Check if active
        if (!$this->is_active) {
            return false;
        }

        // Check start date
        if ($this->starts_at && Carbon::now()->isBefore($this->starts_at)) {
            return false;
        }

        // Check expiry date
        if ($this->expires_at && Carbon::now()->isAfter($this->expires_at)) {
            return false;
        }

        return true;
    }

    /**
     * Check if quantity qualifies for this discount
     */
    public function qualifiesForQuantity(int $quantity): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        if ($quantity < $this->min_quantity) {
            return false;
        }

        if ($this->max_quantity && $quantity > $this->max_quantity) {
            return false;
        }

        return true;
    }

    /**
     * Calculate discount amount for given price and quantity
     */
    public function calculateDiscount(float $unitPrice, int $quantity): float
    {
        if (!$this->qualifiesForQuantity($quantity)) {
            return 0;
        }

        if ($this->discount_type === 'percentage') {
            return $unitPrice * ($this->discount_value / 100);
        } else {
            return min($this->discount_value, $unitPrice);
        }
    }

    /**
     * Get the discounted price per unit
     */
    public function getDiscountedPrice(float $unitPrice, int $quantity): float
    {
        $discount = $this->calculateDiscount($unitPrice, $quantity);
        return max(0, $unitPrice - $discount);
    }

    /**
     * Scope for active discounts
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for valid discounts (not expired)
     */
    public function scopeValid($query)
    {
        $now = Carbon::now();
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>=', $now);
            });
    }

    /**
     * Scope for specific quantity
     */
    public function scopeForQuantity($query, int $quantity)
    {
        return $query->where('min_quantity', '<=', $quantity)
            ->where(function ($q) use ($quantity) {
                $q->whereNull('max_quantity')->orWhere('max_quantity', '>=', $quantity);
            });
    }

    /**
     * Get discount description
     */
    public function getDescriptionAttribute(): string
    {
        $description = "خصم ";
        
        if ($this->discount_type === 'percentage') {
            $description .= $this->discount_value . "%";
        } else {
            $description .= "$" . $this->discount_value;
        }

        $description .= " عند شراء ";
        
        if ($this->max_quantity) {
            $description .= "من " . $this->min_quantity . " إلى " . $this->max_quantity . " قطعة";
        } else {
            $description .= $this->min_quantity . " قطع أو أكثر";
        }

        return $description;
    }

    /**
     * Get savings amount for given price and quantity
     */
    public function getSavingsAmount(float $unitPrice, int $quantity): float
    {
        return $this->calculateDiscount($unitPrice, $quantity) * $quantity;
    }

    /**
     * Check if discount is expired
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && Carbon::now()->isAfter($this->expires_at);
    }

    /**
     * Check if discount is not started yet
     */
    public function getIsNotStartedAttribute(): bool
    {
        return $this->starts_at && Carbon::now()->isBefore($this->starts_at);
    }

    /**
     * Get quantity range text
     */
    public function getQuantityRangeAttribute(): string
    {
        if ($this->max_quantity) {
            return $this->min_quantity . " - " . $this->max_quantity;
        }
        
        return $this->min_quantity . "+";
    }

    /**
     * Get discount value formatted
     */
    public function getFormattedDiscountAttribute(): string
    {
        if ($this->discount_type === 'percentage') {
            return $this->discount_value . "%";
        }
        
        return "$" . number_format($this->discount_value, 2);
    }
}
