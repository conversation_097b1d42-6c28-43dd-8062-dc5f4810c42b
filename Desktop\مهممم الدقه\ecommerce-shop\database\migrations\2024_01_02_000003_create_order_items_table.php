<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('order_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->string('product_name'); // اسم المنتج وقت الطلب
            $table->string('product_sku')->nullable(); // رمز المنتج وقت الطلب
            $table->integer('quantity');
            $table->decimal('unit_price', 10, 2); // سعر الوحدة الأصلي
            $table->decimal('unit_discount', 10, 2)->default(0); // خصم الوحدة
            $table->decimal('final_unit_price', 10, 2); // السعر النهائي للوحدة
            $table->decimal('total_price', 10, 2); // السعر الإجمالي للعنصر
            $table->json('product_options')->nullable(); // خيارات المنتج
            $table->json('applied_discounts')->nullable(); // الخصومات المطبقة
            $table->timestamps();

            $table->index(['order_id', 'product_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('order_items');
    }
};
