<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ShopController;
use App\Http\Controllers\Admin\AdminController;
use App\Http\Controllers\CustomerController;
use Illuminate\Support\Facades\Auth;

// Public Shop Routes
Route::get('/', [ShopController::class, 'index'])->name('shop.index');
Route::get('/shop', [ShopController::class, 'shop'])->name('shop.products');
Route::get('/product/{product}', [ShopController::class, 'product'])->name('shop.product');
Route::get('/category/{category}', [ShopController::class, 'category'])->name('shop.category');
Route::get('/search', [ShopController::class, 'search'])->name('shop.search');
Route::get('/about', [ShopController::class, 'about'])->name('shop.about');
Route::get('/contact', [ShopController::class, 'contact'])->name('shop.contact');
Route::post('/contact', [ShopController::class, 'submitContact'])->name('shop.contact.submit');
Route::get('/track-order', [ShopController::class, 'trackOrder'])->name('shop.track-order');
Route::post('/track-order', [ShopController::class, 'getOrderStatus'])->name('shop.track-order.status');

// AJAX Routes
Route::get('/api/products/category', [ShopController::class, 'getProductsByCategory'])->name('api.products.category');
Route::get('/api/products/search', [ShopController::class, 'searchProducts'])->name('api.products.search');
Route::get('/api/products/{product}/price', [ShopController::class, 'getProductPrice'])->name('api.products.price');

// Authenticated Routes
Route::middleware([
    'auth:sanctum',
    config('jetstream.auth_session'),
    'verified',
])->group(function () {

    // Default Dashboard Route
    Route::get('/dashboard', function () {
        $user = Auth::user();
        if ($user->hasRole('admin')) {
            return redirect()->route('admin.dashboard');
        } elseif ($user->hasRole('customer')) {
            return redirect()->route('customer.dashboard');
        }
        return redirect()->route('customer.dashboard');
    })->name('dashboard');

    // Admin Routes
    Route::middleware(['permission:view reports'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [AdminController::class, 'dashboard'])->name('dashboard');

        // Products Management
        Route::get('/products', [AdminController::class, 'products'])->name('products');
        Route::get('/products/create', [AdminController::class, 'createProduct'])->name('products.create');
        Route::post('/products', [AdminController::class, 'storeProduct'])->name('products.store');
        Route::get('/products/{product}/edit', [AdminController::class, 'editProduct'])->name('products.edit');
        Route::put('/products/{product}', [AdminController::class, 'updateProduct'])->name('products.update');
        Route::delete('/products/{product}', [AdminController::class, 'deleteProduct'])->name('products.delete');

        // Orders Management
        Route::get('/orders', [AdminController::class, 'orders'])->name('orders');
        Route::get('/orders/{order}', [AdminController::class, 'showOrder'])->name('orders.show');
        Route::put('/orders/{order}/status', [AdminController::class, 'updateOrderStatus'])->name('orders.status');

        // Users Management
        Route::get('/users', [AdminController::class, 'users'])->name('users');
        Route::get('/users/{user}/edit', [AdminController::class, 'editUser'])->name('users.edit');
        Route::put('/users/{user}', [AdminController::class, 'updateUser'])->name('users.update');

        // Coupons Management
        Route::get('/coupons', [AdminController::class, 'coupons'])->name('coupons');
        Route::get('/coupons/create', [AdminController::class, 'createCoupon'])->name('coupons.create');
        Route::post('/coupons', [AdminController::class, 'storeCoupon'])->name('coupons.store');
        Route::get('/coupons/{coupon}/edit', [AdminController::class, 'editCoupon'])->name('coupons.edit');
        Route::put('/coupons/{coupon}', [AdminController::class, 'updateCoupon'])->name('coupons.update');
        Route::delete('/coupons/{coupon}', [AdminController::class, 'deleteCoupon'])->name('coupons.delete');

        // Reports
        Route::get('/reports', [AdminController::class, 'reports'])->name('reports');
    });

    // Customer Routes
    Route::middleware(['role:customer'])->prefix('customer')->name('customer.')->group(function () {
        Route::get('/dashboard', [CustomerController::class, 'dashboard'])->name('dashboard');

        // Cart Management
        Route::get('/cart', [CustomerController::class, 'cart'])->name('cart');
        Route::post('/cart/{product}', [CustomerController::class, 'addToCart'])->name('cart.add');
        Route::put('/cart/{cartItem}', [CustomerController::class, 'updateCart'])->name('cart.update');
        Route::delete('/cart/{cartItem}', [CustomerController::class, 'removeFromCart'])->name('cart.remove');
        Route::delete('/cart', [CustomerController::class, 'clearCart'])->name('cart.clear');

        // Checkout
        Route::get('/checkout', [CustomerController::class, 'checkout'])->name('checkout');
        Route::post('/checkout/coupon', [CustomerController::class, 'applyCoupon'])->name('checkout.coupon');
        Route::post('/checkout/order', [CustomerController::class, 'placeOrder'])->name('checkout.order');

        // Orders
        Route::get('/orders', [CustomerController::class, 'orders'])->name('orders');
        Route::get('/orders/{order}', [CustomerController::class, 'showOrder'])->name('orders.show');

        // Profile
        Route::get('/profile', [CustomerController::class, 'profile'])->name('profile');
        Route::put('/profile', [CustomerController::class, 'updateProfile'])->name('profile.update');

        // Wishlist
        Route::get('/wishlist', [CustomerController::class, 'wishlist'])->name('wishlist');
    });
});
