<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Cart;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Coupon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CustomerController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Customer Dashboard
     */
    public function dashboard()
    {
        $user = Auth::user();
        $stats = [
            'total_orders' => $user->orders()->count(),
            'pending_orders' => $user->orders()->where('status', 'pending')->count(),
            'completed_orders' => $user->orders()->where('status', 'completed')->count(),
            'cart_items' => $user->cartItems()->count(),
            'recent_orders' => $user->orders()->with('orderItems.product')->latest()->take(5)->get(),
        ];

        return view('customer.dashboard', compact('stats'));
    }

    /**
     * Cart Management
     */
    public function cart()
    {
        $cartItems = Auth::user()->cartItems()->with('product')->get();
        $total = $cartItems->sum(function ($item) {
            return $item->quantity * $item->product->final_price;
        });

        return view('customer.cart', compact('cartItems', 'total'));
    }

    public function addToCart(Request $request, Product $product)
    {
        $request->validate([
            'quantity' => 'required|integer|min:1|max:' . $product->stock_quantity
        ]);

        $user = Auth::user();
        $cartItem = $user->cartItems()->where('product_id', $product->id)->first();

        if ($cartItem) {
            $newQuantity = $cartItem->quantity + $request->quantity;
            if ($newQuantity > $product->stock_quantity) {
                return redirect()->back()->with('error', 'Not enough stock available!');
            }
            $cartItem->update(['quantity' => $newQuantity]);
        } else {
            Cart::create([
                'user_id' => $user->id,
                'product_id' => $product->id,
                'quantity' => $request->quantity,
            ]);
        }

        return redirect()->back()->with('success', 'Product added to cart successfully!');
    }

    public function updateCart(Request $request, Cart $cartItem)
    {
        $this->authorize('update', $cartItem);

        $request->validate([
            'quantity' => 'required|integer|min:1|max:' . $cartItem->product->stock_quantity
        ]);

        $cartItem->update(['quantity' => $request->quantity]);

        return redirect()->back()->with('success', 'Cart updated successfully!');
    }

    public function removeFromCart(Cart $cartItem)
    {
        $this->authorize('delete', $cartItem);
        
        $cartItem->delete();

        return redirect()->back()->with('success', 'Item removed from cart!');
    }

    public function clearCart()
    {
        Auth::user()->cartItems()->delete();
        return redirect()->back()->with('success', 'Cart cleared successfully!');
    }

    /**
     * Checkout Process
     */
    public function checkout()
    {
        $cartItems = Auth::user()->cartItems()->with('product')->get();
        
        if ($cartItems->isEmpty()) {
            return redirect()->route('customer.cart')->with('error', 'Your cart is empty!');
        }

        $subtotal = $cartItems->sum(function ($item) {
            return $item->quantity * $item->product->final_price;
        });

        return view('customer.checkout', compact('cartItems', 'subtotal'));
    }

    public function applyCoupon(Request $request)
    {
        $request->validate([
            'coupon_code' => 'required|string'
        ]);

        $coupon = Coupon::where('code', $request->coupon_code)
            ->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('expires_at')
                    ->orWhere('expires_at', '>', now());
            })
            ->first();

        if (!$coupon) {
            return response()->json(['error' => 'Invalid or expired coupon code'], 400);
        }

        $cartItems = Auth::user()->cartItems()->with('product')->get();
        $subtotal = $cartItems->sum(function ($item) {
            return $item->quantity * $item->product->final_price;
        });

        if ($coupon->minimum_amount && $subtotal < $coupon->minimum_amount) {
            return response()->json(['error' => 'Minimum order amount not met'], 400);
        }

        $discount = $coupon->calculateDiscount($subtotal);
        $total = $subtotal - $discount;

        return response()->json([
            'success' => true,
            'discount' => $discount,
            'total' => $total,
            'coupon' => $coupon
        ]);
    }

    public function placeOrder(Request $request)
    {
        $request->validate([
            'shipping_address' => 'required|string',
            'payment_method' => 'required|in:cash_on_delivery,credit_card',
            'coupon_code' => 'nullable|string'
        ]);

        $user = Auth::user();
        $cartItems = $user->cartItems()->with('product')->get();

        if ($cartItems->isEmpty()) {
            return redirect()->route('customer.cart')->with('error', 'Your cart is empty!');
        }

        DB::beginTransaction();
        try {
            $subtotal = $cartItems->sum(function ($item) {
                return $item->quantity * $item->product->final_price;
            });

            $discount = 0;
            $coupon = null;

            if ($request->coupon_code) {
                $coupon = Coupon::where('code', $request->coupon_code)
                    ->where('is_active', true)
                    ->first();
                
                if ($coupon && $coupon->canBeUsed($subtotal)) {
                    $discount = $coupon->calculateDiscount($subtotal);
                }
            }

            $total = $subtotal - $discount;

            // Create order
            $order = Order::create([
                'user_id' => $user->id,
                'order_number' => 'ORD-' . time() . '-' . $user->id,
                'subtotal' => $subtotal,
                'discount_amount' => $discount,
                'total_amount' => $total,
                'shipping_address' => $request->shipping_address,
                'payment_method' => $request->payment_method,
                'status' => 'pending',
                'coupon_id' => $coupon ? $coupon->id : null,
            ]);

            // Create order items and update stock
            foreach ($cartItems as $cartItem) {
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $cartItem->product_id,
                    'quantity' => $cartItem->quantity,
                    'price' => $cartItem->product->final_price,
                ]);

                // Update product stock
                $cartItem->product->decrement('stock_quantity', $cartItem->quantity);
            }

            // Record coupon usage
            if ($coupon) {
                $coupon->recordUsage($user->id, $order->id);
            }

            // Clear cart
            $user->cartItems()->delete();

            DB::commit();

            return redirect()->route('customer.orders')->with('success', 'Order placed successfully!');

        } catch (\Exception $e) {
            DB::rollback();
            return redirect()->back()->with('error', 'Failed to place order. Please try again.');
        }
    }

    /**
     * Orders Management
     */
    public function orders()
    {
        $orders = Auth::user()->orders()->with('orderItems.product')->latest()->paginate(10);
        return view('customer.orders', compact('orders'));
    }

    public function showOrder(Order $order)
    {
        $this->authorize('view', $order);
        
        $order->load('orderItems.product');
        return view('customer.order-details', compact('order'));
    }

    /**
     * Profile Management
     */
    public function profile()
    {
        return view('customer.profile');
    }

    public function updateProfile(Request $request)
    {
        $user = Auth::user();
        
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
        ]);

        $user->update($request->only(['name', 'email', 'phone', 'address']));

        return redirect()->back()->with('success', 'Profile updated successfully!');
    }

    /**
     * Wishlist (if needed)
     */
    public function wishlist()
    {
        // Implementation for wishlist functionality
        return view('customer.wishlist');
    }
}
