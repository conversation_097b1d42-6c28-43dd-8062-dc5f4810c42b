<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OrderItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'product_id',
        'product_name',
        'product_sku',
        'quantity',
        'unit_price',
        'unit_discount',
        'final_unit_price',
        'total_price',
        'product_options',
        'applied_discounts'
    ];

    protected $casts = [
        'unit_price' => 'decimal:2',
        'unit_discount' => 'decimal:2',
        'final_unit_price' => 'decimal:2',
        'total_price' => 'decimal:2',
        'product_options' => 'array',
        'applied_discounts' => 'array',
    ];

    /**
     * Get the order that owns the order item
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the product for this order item
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get total discount amount for this item
     */
    public function getTotalDiscountAttribute(): float
    {
        return $this->unit_discount * $this->quantity;
    }

    /**
     * Get savings amount
     */
    public function getSavingsAmountAttribute(): float
    {
        return ($this->unit_price - $this->final_unit_price) * $this->quantity;
    }

    /**
     * Get discount percentage
     */
    public function getDiscountPercentageAttribute(): float
    {
        if ($this->unit_price <= 0) {
            return 0;
        }

        return (($this->unit_price - $this->final_unit_price) / $this->unit_price) * 100;
    }

    /**
     * Check if item has discount
     */
    public function getHasDiscountAttribute(): bool
    {
        return $this->unit_discount > 0;
    }

    /**
     * Get formatted product options
     */
    public function getFormattedOptionsAttribute(): string
    {
        if (empty($this->product_options)) {
            return '';
        }

        $options = [];
        foreach ($this->product_options as $key => $value) {
            $options[] = "{$key}: {$value}";
        }

        return implode(', ', $options);
    }

    /**
     * Get applied discounts summary
     */
    public function getDiscountsSummaryAttribute(): string
    {
        if (empty($this->applied_discounts)) {
            return '';
        }

        $summaries = [];
        foreach ($this->applied_discounts as $discount) {
            $summaries[] = $discount['description'] ?? 'خصم';
        }

        return implode(', ', $summaries);
    }

    /**
     * Get total applied discount amount
     */
    public function getTotalAppliedDiscountAttribute(): float
    {
        if (empty($this->applied_discounts)) {
            return 0;
        }

        $total = 0;
        foreach ($this->applied_discounts as $discount) {
            $total += $discount['amount'] ?? 0;
        }

        return $total * $this->quantity;
    }
}
