import { OperatorFunction } from '../types';
/** @deprecated To be removed in v9. Use {@link map} instead: `map(() => value)`. */
export declare function mapTo<R>(value: R): OperatorFunction<unknown, R>;
/**
 * @deprecated Do not specify explicit type parameters. Signatures with type parameters
 * that cannot be inferred will be removed in v8. `mapTo` itself will be removed in v9,
 * use {@link map} instead: `map(() => value)`.
 * */
export declare function mapTo<T, R>(value: R): OperatorFunction<T, R>;
//# sourceMappingURL=mapTo.d.ts.map