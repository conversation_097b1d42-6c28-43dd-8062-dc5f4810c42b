<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Order;
use Illuminate\Http\Request;

class ShopController extends Controller
{
    /**
     * Homepage
     */
    public function index()
    {
        $featuredProducts = Product::active()->inStock()->featured()->take(8)->get();
        $newProducts = Product::active()->inStock()->latest()->take(8)->get();
        $categories = Product::active()->distinct()->pluck('category');

        return view('shop.index', compact('featuredProducts', 'newProducts', 'categories'));
    }

    /**
     * Shop page with all products
     */
    public function shop(Request $request)
    {
        $query = Product::active()->inStock();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Price range filter
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortOrder);
                break;
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'popularity':
                $query->withCount('orderItems')->orderBy('order_items_count', $sortOrder);
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        $products = $query->paginate(12);
        $categories = Product::active()->distinct()->pluck('category');
        
        // Price range for filter
        $priceRange = [
            'min' => Product::active()->min('price'),
            'max' => Product::active()->max('price')
        ];

        return view('shop.products', compact('products', 'categories', 'priceRange'));
    }

    /**
     * Product details page
     */
    public function product(Product $product)
    {
        // Only show active products
        if ($product->status !== 'active') {
            abort(404);
        }

        // Get related products from same category
        $relatedProducts = Product::active()
            ->inStock()
            ->byCategory($product->category)
            ->where('id', '!=', $product->id)
            ->limit(4)
            ->get();

        // Get quantity discounts for this product
        $quantityDiscounts = $product->quantityDiscounts()->orderBy('min_quantity')->get();

        return view('shop.product-details', compact('product', 'relatedProducts', 'quantityDiscounts'));
    }

    /**
     * Category page
     */
    public function category(Request $request, $category)
    {
        $query = Product::active()->inStock()->byCategory($category);

        // Search within category
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Price range filter
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortOrder);
                break;
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'popularity':
                $query->withCount('orderItems')->orderBy('order_items_count', $sortOrder);
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        $products = $query->paginate(12);
        
        // Price range for filter
        $priceRange = [
            'min' => Product::active()->byCategory($category)->min('price'),
            'max' => Product::active()->byCategory($category)->max('price')
        ];

        return view('shop.category', compact('products', 'category', 'priceRange'));
    }

    /**
     * Search results page
     */
    public function search(Request $request)
    {
        $searchTerm = $request->get('q');
        
        if (!$searchTerm) {
            return redirect()->route('shop.index');
        }

        $query = Product::active()->inStock()->search($searchTerm);

        // Category filter
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Price range filter
        if ($request->has('min_price') && $request->min_price) {
            $query->where('price', '>=', $request->min_price);
        }

        if ($request->has('max_price') && $request->max_price) {
            $query->where('price', '<=', $request->max_price);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        switch ($sortBy) {
            case 'name':
                $query->orderBy('name', $sortOrder);
                break;
            case 'price':
                $query->orderBy('price', $sortOrder);
                break;
            case 'popularity':
                $query->withCount('orderItems')->orderBy('order_items_count', $sortOrder);
                break;
            default:
                $query->orderBy('created_at', $sortOrder);
        }

        $products = $query->paginate(12);
        $categories = Product::active()->distinct()->pluck('category');
        
        // Price range for filter
        $priceRange = [
            'min' => Product::active()->min('price'),
            'max' => Product::active()->max('price')
        ];

        return view('shop.search-results', compact('products', 'searchTerm', 'categories', 'priceRange'));
    }

    /**
     * AJAX endpoints for dynamic functionality
     */
    public function getProductsByCategory(Request $request)
    {
        $category = $request->get('category');
        
        $products = Product::active()
            ->inStock()
            ->byCategory($category)
            ->get(['id', 'name', 'price', 'discount_price', 'image']);

        return response()->json($products);
    }

    public function searchProducts(Request $request)
    {
        $search = $request->get('q');
        
        $products = Product::active()
            ->inStock()
            ->search($search)
            ->limit(10)
            ->get(['id', 'name', 'price', 'discount_price', 'image']);

        return response()->json($products);
    }

    public function getProductPrice(Request $request, Product $product)
    {
        $quantity = $request->get('quantity', 1);
        
        $basePrice = $product->final_price;
        $totalPrice = $basePrice * $quantity;
        
        // Check for quantity discounts
        $discount = $product->getQuantityDiscount($quantity);
        if ($discount) {
            $discountAmount = ($basePrice * $quantity * $discount->discount_percentage) / 100;
            $totalPrice -= $discountAmount;
        }

        return response()->json([
            'base_price' => $basePrice,
            'total_price' => $totalPrice,
            'discount' => $discount ? $discount->discount_percentage : 0,
            'savings' => $discount ? $discountAmount : 0
        ]);
    }

    /**
     * About page
     */
    public function about()
    {
        return view('shop.about');
    }

    /**
     * Contact page
     */
    public function contact()
    {
        return view('shop.contact');
    }

    /**
     * Contact form submission
     */
    public function submitContact(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:1000'
        ]);

        // Here you can implement email sending or save to database
        // For now, just return success message
        
        return redirect()->back()->with('success', 'Thank you for your message! We will get back to you soon.');
    }

    /**
     * Track order page
     */
    public function trackOrder()
    {
        return view('shop.track-order');
    }

    /**
     * Track order by number
     */
    public function getOrderStatus(Request $request)
    {
        $request->validate([
            'order_number' => 'required|string'
        ]);

        $order = Order::where('order_number', $request->order_number)->first();

        if (!$order) {
            return response()->json(['error' => 'Order not found'], 404);
        }

        return response()->json([
            'order_number' => $order->order_number,
            'status' => $order->status,
            'created_at' => $order->created_at->format('Y-m-d H:i:s'),
            'total_amount' => $order->total_amount
        ]);
    }
}
