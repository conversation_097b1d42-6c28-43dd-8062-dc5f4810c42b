<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('carts', function (Blueprint $table) {
            $table->id();
            $table->string('session_id')->nullable(); // للمستخدمين غير المسجلين
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade'); // للمستخدمين المسجلين
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->integer('quantity');
            $table->decimal('price', 10, 2); // سعر المنتج وقت الإضافة
            $table->json('product_options')->nullable(); // خيارات المنتج (لون، حجم، إلخ)
            $table->timestamps();

            $table->index(['session_id', 'user_id']);
            $table->unique(['session_id', 'product_id', 'user_id'], 'cart_unique_item');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('carts');
    }
};
