<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;

class Product extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'price',
        'quantity',
        'image',
        'category',
        'status',
        'discount_price',
        'sku',
        'specifications'
    ];

    protected $casts = [
        'specifications' => 'array',
        'price' => 'decimal:2',
        'discount_price' => 'decimal:2',
    ];

    /**
     * Get the product's final price (considering discount)
     */
    protected function finalPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->discount_price ?? $this->price,
        );
    }

    /**
     * Check if product is on sale
     */
    protected function isOnSale(): Attribute
    {
        return Attribute::make(
            get: fn () => !is_null($this->discount_price) && $this->discount_price < $this->price,
        );
    }

    /**
     * Check if product is in stock
     */
    protected function inStock(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->quantity > 0,
        );
    }

    /**
     * Get discount percentage
     */
    protected function discountPercentage(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->is_on_sale 
                ? round((($this->price - $this->discount_price) / $this->price) * 100)
                : 0,
        );
    }

    /**
     * Scope for active products
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for products in stock
     */
    public function scopeInStock($query)
    {
        return $query->where('quantity', '>', 0);
    }

    /**
     * Scope for products by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for search
     */
    public function scopeSearch($query, $search)
    {
        return $query->where(function ($q) use ($search) {
            $q->where('name', 'like', "%{$search}%")
              ->orWhere('description', 'like', "%{$search}%")
              ->orWhere('category', 'like', "%{$search}%");
        });
    }

    /**
     * Get quantity discounts for this product
     */
    public function quantityDiscounts()
    {
        return $this->hasMany(QuantityDiscount::class);
    }

    /**
     * Get active quantity discounts
     */
    public function activeQuantityDiscounts()
    {
        return $this->quantityDiscounts()->valid();
    }

    /**
     * Get quantity discount for specific quantity
     */
    public function getQuantityDiscount(int $quantity): ?QuantityDiscount
    {
        return $this->activeQuantityDiscounts()
            ->forQuantity($quantity)
            ->orderBy('discount_value', 'desc')
            ->first();
    }

    /**
     * Get all available quantity discounts
     */
    public function getAvailableQuantityDiscounts()
    {
        return $this->activeQuantityDiscounts()
            ->orderBy('min_quantity')
            ->get();
    }

    /**
     * Check if product has quantity discounts
     */
    public function hasQuantityDiscounts(): bool
    {
        return $this->activeQuantityDiscounts()->exists();
    }

    /**
     * Get cart items for this product
     */
    public function cartItems()
    {
        return $this->hasMany(Cart::class);
    }

    /**
     * Get order items for this product
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }
}
