[2025-06-02 10:50:01] local.ERROR: SQLSTATE[HY000]: General error: 1 duplicate column name: two_factor_secret (Connection: sqlite, SQL: alter table "users" add column "two_factor_secret" text) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 duplicate column name: two_factor_secret (Connection: sqlite, SQL: alter table \"users\" add column \"two_factor_secret\" text) at C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"us...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"us...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"us...')
#3 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('users', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\database\\migrations\\2025_06_02_104922_add_two_factor_columns_to_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_02_1049...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_02_1049...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 2, false)
#15 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 duplicate column name: two_factor_secret at C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('alter table \"us...')
#1 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table \"us...', Array)
#2 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"us...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"us...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"us...')
#5 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('users', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\database\\migrations\\2025_06_02_104922_add_two_factor_columns_to_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_02_1049...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_02_1049...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 2, false)
#17 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-02 10:50:11] local.ERROR: SQLSTATE[HY000]: General error: 1 duplicate column name: two_factor_secret (Connection: sqlite, SQL: alter table "users" add column "two_factor_secret" text) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1 duplicate column name: two_factor_secret (Connection: sqlite, SQL: alter table \"users\" add column \"two_factor_secret\" text) at C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"us...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"us...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"us...')
#3 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('users', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\database\\migrations\\2025_06_02_104922_add_two_factor_columns_to_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#7 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_02_1049...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_02_1049...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#15 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1 duplicate column name: two_factor_secret at C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:562)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(562): PDO->prepare('alter table \"us...')
#1 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table \"us...', Array)
#2 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table \"us...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table \"us...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table \"us...')
#5 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(460): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->table('users', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\database\\migrations\\2025_06_02_104922_add_two_factor_columns_to_users_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#9 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\SQLiteConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2025_06_02_1049...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_06_02_1049...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 3, false)
#17 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
[2025-06-02 10:53:04] local.ERROR: SQLSTATE[HY000]: General error: 1005 Can't create table `laravel`.`orders` (errno: 150 "Foreign key constraint is incorrectly formed") (Connection: mysql, SQL: alter table `orders` add constraint `orders_coupon_id_foreign` foreign key (`coupon_id`) references `coupons` (`id`) on delete set null) {"exception":"[object] (Illuminate\\Database\\QueryException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `laravel`.`orders` (errno: 150 \"Foreign key constraint is incorrectly formed\") (Connection: mysql, SQL: alter table `orders` add constraint `orders_coupon_id_foreign` foreign key (`coupon_id`) references `coupons` (`id`) on delete set null) at C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `or...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `or...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `or...')
#3 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#4 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#5 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('orders', Object(Closure))
#6 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\database\\migrations\\2024_01_02_000002_create_orders_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#7 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#8 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#9 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#10 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_01_02_0000...', Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_02_0000...', Object(Closure))
#14 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#15 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#16 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#17 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#18 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#20 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#21 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#22 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#25 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#26 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#27 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#28 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#29 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#30 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#34 {main}

[previous exception] [object] (PDOException(code: HY000): SQLSTATE[HY000]: General error: 1005 Can't create table `laravel`.`orders` (errno: 150 \"Foreign key constraint is incorrectly formed\") at C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:568)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(568): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('alter table `or...', Array)
#2 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('alter table `or...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(557): Illuminate\\Database\\Connection->run('alter table `or...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Blueprint.php(119): Illuminate\\Database\\Connection->statement('alter table `or...')
#5 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(618): Illuminate\\Database\\Schema\\Blueprint->build()
#6 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Schema\\Builder.php(472): Illuminate\\Database\\Schema\\Builder->build(Object(Illuminate\\Database\\Schema\\Blueprint))
#7 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Facades\\Facade.php(361): Illuminate\\Database\\Schema\\Builder->create('orders', Object(Closure))
#8 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\database\\migrations\\2024_01_02_000002_create_orders_table.php(14): Illuminate\\Support\\Facades\\Facade::__callStatic('create', Array)
#9 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(514): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#10 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(439): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#11 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(448): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#12 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#13 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\View\\Components\\Task.php(41): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#14 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(805): Illuminate\\Console\\View\\Components\\Task->render('2024_01_02_0000...', Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(250): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2024_01_02_0000...', Object(Closure))
#16 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(210): Illuminate\\Database\\Migrations\\Migrator->runUp('C:\\\\Users\\\\<USER>\\\\D...', 1, false)
#17 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(137): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#18 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(116): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#19 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(665): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#20 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(109): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(88): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->runMigrations()
#22 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#23 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(754): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#28 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(1092): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(341): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\symfony\\console\\Application.php(192): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1234): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\Desktop\\مهممم الدقه\\ecommerce-shop\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#36 {main}
"} 
