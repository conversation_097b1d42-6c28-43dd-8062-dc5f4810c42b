<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;

class ProductController extends Controller
{
    /**
     * Display a listing of products for users
     */
    public function index(Request $request)
    {
        $query = Product::active()->inStock();

        // Search functionality
        if ($request->has('search') && $request->search) {
            $query->search($request->search);
        }

        // Category filter
        if ($request->has('category') && $request->category) {
            $query->byCategory($request->category);
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        
        if (in_array($sortBy, ['name', 'price', 'created_at'])) {
            $query->orderBy($sortBy, $sortOrder);
        }

        $products = $query->paginate(12);
        $categories = Product::active()->distinct()->pluck('category');

        return view('products.index', compact('products', 'categories'));
    }

    /**
     * Display the specified product
     */
    public function show(Product $product)
    {
        // Only show active products to users
        if ($product->status !== 'active') {
            abort(404);
        }

        // Get related products from same category
        $relatedProducts = Product::active()
            ->inStock()
            ->byCategory($product->category)
            ->where('id', '!=', $product->id)
            ->limit(4)
            ->get();

        return view('products.show', compact('product', 'relatedProducts'));
    }

    /**
     * Get products by category (AJAX)
     */
    public function getByCategory(Request $request)
    {
        $category = $request->get('category');
        
        $products = Product::active()
            ->inStock()
            ->byCategory($category)
            ->get(['id', 'name', 'price', 'discount_price', 'image']);

        return response()->json($products);
    }

    /**
     * Search products (AJAX)
     */
    public function search(Request $request)
    {
        $search = $request->get('q');
        
        $products = Product::active()
            ->inStock()
            ->search($search)
            ->limit(10)
            ->get(['id', 'name', 'price', 'discount_price', 'image']);

        return response()->json($products);
    }
}
