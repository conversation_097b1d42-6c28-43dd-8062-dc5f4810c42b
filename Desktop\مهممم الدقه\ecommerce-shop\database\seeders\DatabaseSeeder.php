<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            RolePermissionSeeder::class,
            ProductSeeder::class,
        ]);

        // Create Admin User
        $admin = User::factory()->withPersonalTeam()->create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $admin->assignRole('admin');

        // Create Customer User
        $customer = User::factory()->withPersonalTeam()->create([
            'name' => 'Customer User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $customer->assignRole('customer');

        // Create Test User
        $testUser = User::factory()->withPersonalTeam()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
        ]);
        $testUser->assignRole('customer');
    }
}
