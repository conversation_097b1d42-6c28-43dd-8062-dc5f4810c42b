{"version": 3, "file": "windowWhen.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/windowWhen.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AAErC,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAChE,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AA+CpD,MAAM,UAAU,UAAU,CAAI,eAA2C;IACvE,OAAO,OAAO,CAAC,UAAC,MAAM,EAAE,UAAU;QAChC,IAAI,MAAyB,CAAC;QAC9B,IAAI,iBAA8C,CAAC;QAMnD,IAAM,WAAW,GAAG,UAAC,GAAQ;YAC3B,MAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;YACnB,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACxB,CAAC,CAAC;QAQF,IAAM,UAAU,GAAG;YAGjB,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,WAAW,EAAE,CAAC;YAGjC,MAAM,aAAN,MAAM,uBAAN,MAAM,CAAE,QAAQ,EAAE,CAAC;YAGnB,MAAM,GAAG,IAAI,OAAO,EAAK,CAAC;YAC1B,UAAU,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;YAGvC,IAAI,eAAgC,CAAC;YACrC,IAAI;gBACF,eAAe,GAAG,SAAS,CAAC,eAAe,EAAE,CAAC,CAAC;aAChD;YAAC,OAAO,GAAG,EAAE;gBACZ,WAAW,CAAC,GAAG,CAAC,CAAC;gBACjB,OAAO;aACR;YAMD,eAAe,CAAC,SAAS,CAAC,CAAC,iBAAiB,GAAG,wBAAwB,CAAC,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;QAC7H,CAAC,CAAC;QAGF,UAAU,EAAE,CAAC;QAGb,MAAM,CAAC,SAAS,CACd,wBAAwB,CACtB,UAAU,EACV,UAAC,KAAK,IAAK,OAAA,MAAO,CAAC,IAAI,CAAC,KAAK,CAAC,EAAnB,CAAmB,EAC9B;YAEE,MAAO,CAAC,QAAQ,EAAE,CAAC;YACnB,UAAU,CAAC,QAAQ,EAAE,CAAC;QACxB,CAAC,EACD,WAAW,EACX;YAGE,iBAAiB,aAAjB,iBAAiB,uBAAjB,iBAAiB,CAAE,WAAW,EAAE,CAAC;YACjC,MAAM,GAAG,IAAK,CAAC;QACjB,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC,CAAC;AACL,CAAC"}