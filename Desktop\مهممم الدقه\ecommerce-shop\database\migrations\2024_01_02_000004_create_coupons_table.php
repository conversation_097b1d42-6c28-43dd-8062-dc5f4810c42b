<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('coupons', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['percentage', 'fixed']); // نسبة مئوية أو مبلغ ثابت
            $table->decimal('value', 10, 2); // قيمة الخصم
            $table->decimal('minimum_amount', 10, 2)->nullable(); // الحد الأدنى للطلب
            $table->decimal('maximum_discount', 10, 2)->nullable(); // الحد الأقصى للخصم
            $table->integer('usage_limit')->nullable(); // عدد مرات الاستخدام المسموح
            $table->integer('used_count')->default(0); // عدد مرات الاستخدام الفعلي
            $table->integer('usage_limit_per_user')->nullable(); // عدد مرات الاستخدام لكل مستخدم
            $table->datetime('starts_at')->nullable(); // تاريخ البداية
            $table->datetime('expires_at')->nullable(); // تاريخ الانتهاء
            $table->boolean('is_active')->default(true);
            $table->json('applicable_products')->nullable(); // المنتجات المطبق عليها
            $table->json('applicable_categories')->nullable(); // الفئات المطبق عليها
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('coupons');
    }
};
