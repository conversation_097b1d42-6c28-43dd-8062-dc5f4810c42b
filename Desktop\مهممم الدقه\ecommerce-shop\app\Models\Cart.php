<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Cart extends Model
{
    use HasFactory;

    protected $fillable = [
        'session_id',
        'user_id',
        'product_id',
        'quantity',
        'price',
        'product_options'
    ];

    protected $casts = [
        'product_options' => 'array',
        'price' => 'decimal:2',
    ];

    /**
     * Get the user that owns the cart item
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the product for this cart item
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get the total price for this cart item
     */
    public function getTotalPriceAttribute(): float
    {
        return $this->quantity * $this->getFinalPriceAttribute();
    }

    /**
     * Get the final price considering quantity discounts
     */
    public function getFinalPriceAttribute(): float
    {
        $product = $this->product;
        if (!$product) {
            return $this->price;
        }

        // Check for quantity discounts
        $quantityDiscount = $product->getQuantityDiscount($this->quantity);
        if ($quantityDiscount) {
            if ($quantityDiscount->discount_type === 'percentage') {
                return $this->price * (1 - $quantityDiscount->discount_value / 100);
            } else {
                return max(0, $this->price - $quantityDiscount->discount_value);
            }
        }

        return $this->price;
    }

    /**
     * Get the discount amount for this cart item
     */
    public function getDiscountAmountAttribute(): float
    {
        return ($this->price - $this->getFinalPriceAttribute()) * $this->quantity;
    }

    /**
     * Scope for user cart items
     */
    public function scopeForUser($query, $userId = null, $sessionId = null)
    {
        if ($userId) {
            return $query->where('user_id', $userId);
        }
        
        if ($sessionId) {
            return $query->where('session_id', $sessionId)->whereNull('user_id');
        }
        
        return $query->whereNull('user_id')->whereNull('session_id');
    }

    /**
     * Scope for active products only
     */
    public function scopeWithActiveProducts($query)
    {
        return $query->whereHas('product', function ($q) {
            $q->where('status', 'active')->where('quantity', '>', 0);
        });
    }

    /**
     * Update cart item quantity
     */
    public function updateQuantity(int $quantity): bool
    {
        if ($quantity <= 0) {
            return $this->delete();
        }

        // Check if quantity is available
        if ($this->product && $this->product->quantity < $quantity) {
            return false;
        }

        $this->quantity = $quantity;
        return $this->save();
    }

    /**
     * Get cart summary for user/session
     */
    public static function getCartSummary($userId = null, $sessionId = null): array
    {
        $cartItems = self::forUser($userId, $sessionId)
            ->withActiveProducts()
            ->with('product')
            ->get();

        $subtotal = 0;
        $totalDiscount = 0;
        $itemCount = 0;

        foreach ($cartItems as $item) {
            $subtotal += $item->quantity * $item->price;
            $totalDiscount += $item->discount_amount;
            $itemCount += $item->quantity;
        }

        $total = $subtotal - $totalDiscount;

        return [
            'items' => $cartItems,
            'item_count' => $itemCount,
            'subtotal' => $subtotal,
            'total_discount' => $totalDiscount,
            'total' => $total,
        ];
    }

    /**
     * Add item to cart
     */
    public static function addItem(int $productId, int $quantity, array $options = [], $userId = null, $sessionId = null): bool
    {
        $product = Product::find($productId);
        if (!$product || $product->status !== 'active' || $product->quantity < $quantity) {
            return false;
        }

        $existingItem = self::forUser($userId, $sessionId)
            ->where('product_id', $productId)
            ->first();

        if ($existingItem) {
            $newQuantity = $existingItem->quantity + $quantity;
            if ($product->quantity < $newQuantity) {
                return false;
            }
            $existingItem->quantity = $newQuantity;
            $existingItem->product_options = array_merge($existingItem->product_options ?? [], $options);
            return $existingItem->save();
        } else {
            return self::create([
                'user_id' => $userId,
                'session_id' => $sessionId,
                'product_id' => $productId,
                'quantity' => $quantity,
                'price' => $product->final_price,
                'product_options' => $options,
            ]) !== null;
        }
    }

    /**
     * Remove item from cart
     */
    public static function removeItem(int $productId, $userId = null, $sessionId = null): bool
    {
        return self::forUser($userId, $sessionId)
            ->where('product_id', $productId)
            ->delete() > 0;
    }

    /**
     * Clear cart
     */
    public static function clearCart($userId = null, $sessionId = null): bool
    {
        return self::forUser($userId, $sessionId)->delete() > 0;
    }

    /**
     * Transfer cart from session to user
     */
    public static function transferToUser(string $sessionId, int $userId): void
    {
        $sessionItems = self::where('session_id', $sessionId)->whereNull('user_id')->get();
        
        foreach ($sessionItems as $sessionItem) {
            $existingUserItem = self::where('user_id', $userId)
                ->where('product_id', $sessionItem->product_id)
                ->first();

            if ($existingUserItem) {
                // Merge quantities
                $existingUserItem->quantity += $sessionItem->quantity;
                $existingUserItem->save();
                $sessionItem->delete();
            } else {
                // Transfer to user
                $sessionItem->update([
                    'user_id' => $userId,
                    'session_id' => null,
                ]);
            }
        }
    }
}
