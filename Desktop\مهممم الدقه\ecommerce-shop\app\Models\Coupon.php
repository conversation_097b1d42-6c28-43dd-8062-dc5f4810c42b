<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Carbon\Carbon;

class Coupon extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'type',
        'value',
        'minimum_amount',
        'maximum_discount',
        'usage_limit',
        'used_count',
        'usage_limit_per_user',
        'starts_at',
        'expires_at',
        'is_active',
        'applicable_products',
        'applicable_categories'
    ];

    protected $casts = [
        'value' => 'decimal:2',
        'minimum_amount' => 'decimal:2',
        'maximum_discount' => 'decimal:2',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
        'applicable_products' => 'array',
        'applicable_categories' => 'array',
    ];

    /**
     * Get the coupon usages
     */
    public function usages(): HasMany
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Get the orders that used this coupon
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Check if coupon is valid
     */
    public function isValid(): bool
    {
        // Check if active
        if (!$this->is_active) {
            return false;
        }

        // Check start date
        if ($this->starts_at && Carbon::now()->isBefore($this->starts_at)) {
            return false;
        }

        // Check expiry date
        if ($this->expires_at && Carbon::now()->isAfter($this->expires_at)) {
            return false;
        }

        // Check usage limit
        if ($this->usage_limit && $this->used_count >= $this->usage_limit) {
            return false;
        }

        return true;
    }

    /**
     * Check if coupon is valid for user
     */
    public function isValidForUser(int $userId): bool
    {
        if (!$this->isValid()) {
            return false;
        }

        // Check per-user usage limit
        if ($this->usage_limit_per_user) {
            $userUsageCount = $this->usages()->where('user_id', $userId)->count();
            if ($userUsageCount >= $this->usage_limit_per_user) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if coupon applies to cart
     */
    public function appliesTo(array $cartItems): bool
    {
        // If no specific products/categories, applies to all
        if (empty($this->applicable_products) && empty($this->applicable_categories)) {
            return true;
        }

        foreach ($cartItems as $item) {
            $product = $item['product'] ?? $item->product;
            
            // Check if product is in applicable products
            if (!empty($this->applicable_products) && in_array($product->id, $this->applicable_products)) {
                return true;
            }

            // Check if product category is in applicable categories
            if (!empty($this->applicable_categories) && in_array($product->category, $this->applicable_categories)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate discount amount for cart
     */
    public function calculateDiscount(float $cartTotal, array $cartItems = []): float
    {
        // Check minimum amount
        if ($this->minimum_amount && $cartTotal < $this->minimum_amount) {
            return 0;
        }

        // Check if coupon applies to cart items
        if (!empty($cartItems) && !$this->appliesTo($cartItems)) {
            return 0;
        }

        $discount = 0;

        if ($this->type === 'percentage') {
            $discount = $cartTotal * ($this->value / 100);
        } else {
            $discount = $this->value;
        }

        // Apply maximum discount limit
        if ($this->maximum_discount && $discount > $this->maximum_discount) {
            $discount = $this->maximum_discount;
        }

        // Ensure discount doesn't exceed cart total
        return min($discount, $cartTotal);
    }

    /**
     * Apply coupon to order
     */
    public function applyToOrder(Order $order, int $userId): bool
    {
        if (!$this->isValidForUser($userId)) {
            return false;
        }

        $discountAmount = $this->calculateDiscount($order->subtotal);
        
        if ($discountAmount <= 0) {
            return false;
        }

        // Update order
        $order->update([
            'coupon_id' => $this->id,
            'coupon_code' => $this->code,
            'coupon_discount' => $discountAmount,
            'discount_amount' => $order->discount_amount + $discountAmount,
            'total_amount' => $order->subtotal + $order->tax_amount + $order->shipping_amount - ($order->discount_amount + $discountAmount),
        ]);

        // Record usage
        CouponUsage::create([
            'coupon_id' => $this->id,
            'user_id' => $userId,
            'order_id' => $order->id,
            'discount_amount' => $discountAmount,
        ]);

        // Increment usage count
        $this->increment('used_count');

        return true;
    }

    /**
     * Scope for active coupons
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for valid coupons (not expired)
     */
    public function scopeValid($query)
    {
        $now = Carbon::now();
        return $query->where('is_active', true)
            ->where(function ($q) use ($now) {
                $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
            })
            ->where(function ($q) use ($now) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>=', $now);
            })
            ->where(function ($q) {
                $q->whereNull('usage_limit')->orWhereRaw('used_count < usage_limit');
            });
    }

    /**
     * Find coupon by code
     */
    public static function findByCode(string $code): ?self
    {
        return self::where('code', strtoupper($code))->first();
    }

    /**
     * Generate unique coupon code
     */
    public static function generateCode(int $length = 8): string
    {
        do {
            $code = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, $length));
        } while (self::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get remaining usage count
     */
    public function getRemainingUsageAttribute(): ?int
    {
        if (!$this->usage_limit) {
            return null;
        }

        return max(0, $this->usage_limit - $this->used_count);
    }

    /**
     * Get usage percentage
     */
    public function getUsagePercentageAttribute(): float
    {
        if (!$this->usage_limit) {
            return 0;
        }

        return ($this->used_count / $this->usage_limit) * 100;
    }

    /**
     * Check if coupon is expired
     */
    public function getIsExpiredAttribute(): bool
    {
        return $this->expires_at && Carbon::now()->isAfter($this->expires_at);
    }

    /**
     * Check if coupon is not started yet
     */
    public function getIsNotStartedAttribute(): bool
    {
        return $this->starts_at && Carbon::now()->isBefore($this->starts_at);
    }
}
