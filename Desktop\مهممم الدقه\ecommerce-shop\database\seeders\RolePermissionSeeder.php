<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\User;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Product permissions
            'view products',
            'create products',
            'edit products',
            'delete products',

            // Order permissions
            'view orders',
            'edit orders',
            'delete orders',

            // User permissions
            'view users',
            'edit users',
            'delete users',

            // Coupon permissions
            'view coupons',
            'create coupons',
            'edit coupons',
            'delete coupons',

            // Report permissions
            'view reports',

            // Customer permissions
            'place orders',
            'view own orders',
            'manage cart',
            'manage profile',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $customerRole = Role::firstOrCreate(['name' => 'customer']);
        $managerRole = Role::firstOrCreate(['name' => 'manager']);

        // Assign permissions to admin role
        $adminRole->givePermissionTo([
            'view products',
            'create products',
            'edit products',
            'delete products',
            'view orders',
            'edit orders',
            'delete orders',
            'view users',
            'edit users',
            'delete users',
            'view coupons',
            'create coupons',
            'edit coupons',
            'delete coupons',
            'view reports',
        ]);

        // Assign permissions to manager role
        $managerRole->givePermissionTo([
            'view products',
            'create products',
            'edit products',
            'view orders',
            'edit orders',
            'view coupons',
            'create coupons',
            'edit coupons',
            'view reports',
        ]);

        // Assign permissions to customer role
        $customerRole->givePermissionTo([
            'place orders',
            'view own orders',
            'manage cart',
            'manage profile',
        ]);

        // Create admin user if not exists
        $adminUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Assign admin role to admin user
        if (!$adminUser->hasRole('admin')) {
            $adminUser->assignRole('admin');
        }

        // Create manager user if not exists
        $managerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Manager User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Assign manager role to manager user
        if (!$managerUser->hasRole('manager')) {
            $managerUser->assignRole('manager');
        }

        // Create customer user if not exists
        $customerUser = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Customer User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );

        // Assign customer role to customer user
        if (!$customerUser->hasRole('customer')) {
            $customerUser->assignRole('customer');
        }
    }
}
