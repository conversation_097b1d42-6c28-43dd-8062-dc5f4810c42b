/* ===================================
   PRINT STYLES
   Optimized for printing and PDF export
   =================================== */

@media print {
    /* ===== RESET PRINT STYLES ===== */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    /* ===== PAGE SETUP ===== */
    @page {
        margin: 1cm;
        size: A4;
    }

    @page :first {
        margin-top: 2cm;
    }

    /* ===== HIDE ELEMENTS ===== */
    .no-print,
    .btn,
    button,
    .navbar,
    .sidebar,
    .admin-sidebar,
    .header-actions,
    .notification-btn,
    .user-dropdown,
    .action-buttons,
    .pagination,
    .modal,
    .tooltip,
    .dropdown-menu,
    nav,
    .nav-tabs,
    .breadcrumb,
    .alert-dismissible .btn-close,
    .form-control,
    input,
    select,
    textarea,
    .search-bar,
    .filters-sidebar,
    .product-actions,
    .hover-effects {
        display: none !important;
    }

    /* ===== LAYOUT ADJUSTMENTS ===== */
    body {
        font-family: 'Times New Roman', serif !important;
        font-size: 12pt !important;
        line-height: 1.4 !important;
        color: #000 !important;
        background: white !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .admin-main {
        margin-left: 0 !important;
        padding: 0 !important;
    }

    .admin-content {
        padding: 0 !important;
        margin: 0 !important;
    }

    .container,
    .container-fluid {
        width: 100% !important;
        max-width: none !important;
        padding: 0 !important;
        margin: 0 !important;
    }

    .row {
        margin: 0 !important;
    }

    .col,
    [class*="col-"] {
        padding: 0 !important;
        margin-bottom: 1rem !important;
    }

    /* ===== TYPOGRAPHY ===== */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        page-break-after: avoid !important;
        margin-top: 1rem !important;
        margin-bottom: 0.5rem !important;
    }

    h1 {
        font-size: 18pt !important;
        font-weight: bold !important;
        border-bottom: 2px solid #000 !important;
        padding-bottom: 0.5rem !important;
    }

    h2 {
        font-size: 16pt !important;
        font-weight: bold !important;
    }

    h3 {
        font-size: 14pt !important;
        font-weight: bold !important;
    }

    h4, h5, h6 {
        font-size: 12pt !important;
        font-weight: bold !important;
    }

    p {
        margin-bottom: 0.5rem !important;
        orphans: 3 !important;
        widows: 3 !important;
    }

    /* ===== TABLES ===== */
    table {
        width: 100% !important;
        border-collapse: collapse !important;
        margin-bottom: 1rem !important;
        page-break-inside: avoid !important;
    }

    th, td {
        border: 1px solid #000 !important;
        padding: 0.5rem !important;
        text-align: left !important;
        vertical-align: top !important;
        font-size: 10pt !important;
    }

    th {
        background-color: #f0f0f0 !important;
        font-weight: bold !important;
        text-transform: uppercase !important;
    }

    tr {
        page-break-inside: avoid !important;
    }

    thead {
        display: table-header-group !important;
    }

    tfoot {
        display: table-footer-group !important;
    }

    /* ===== CARDS AND SECTIONS ===== */
    .card,
    .stat-card,
    .custom-table {
        border: 1px solid #000 !important;
        margin-bottom: 1rem !important;
        page-break-inside: avoid !important;
        background: white !important;
        box-shadow: none !important;
    }

    .card-header,
    .table-header {
        background-color: #f0f0f0 !important;
        color: #000 !important;
        border-bottom: 1px solid #000 !important;
        padding: 0.5rem !important;
        font-weight: bold !important;
    }

    .card-body {
        padding: 0.5rem !important;
    }

    /* ===== IMAGES ===== */
    img {
        max-width: 100% !important;
        height: auto !important;
        page-break-inside: avoid !important;
        border: 1px solid #ccc !important;
    }

    .product-image img,
    .user-avatar,
    .stat-icon {
        width: 2cm !important;
        height: 2cm !important;
        object-fit: cover !important;
    }

    /* ===== BADGES AND STATUS ===== */
    .badge,
    .status-badge {
        border: 1px solid #000 !important;
        padding: 0.2rem 0.4rem !important;
        font-size: 8pt !important;
        font-weight: bold !important;
        background: white !important;
        color: #000 !important;
    }

    /* ===== LINKS ===== */
    a {
        color: #000 !important;
        text-decoration: underline !important;
    }

    a[href]:after {
        content: " (" attr(href) ")" !important;
        font-size: 8pt !important;
        color: #666 !important;
    }

    a[href^="#"]:after,
    a[href^="javascript:"]:after,
    a[href^="mailto:"]:after,
    a[href^="tel:"]:after {
        content: "" !important;
    }

    /* ===== PAGE BREAKS ===== */
    .page-break {
        page-break-before: always !important;
    }

    .page-break-after {
        page-break-after: always !important;
    }

    .no-page-break {
        page-break-inside: avoid !important;
    }

    /* ===== INVOICE/RECEIPT STYLES ===== */
    .invoice-header {
        text-align: center !important;
        margin-bottom: 2rem !important;
        border-bottom: 2px solid #000 !important;
        padding-bottom: 1rem !important;
    }

    .invoice-details {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 2rem !important;
    }

    .invoice-details > div {
        width: 48% !important;
    }

    .invoice-total {
        text-align: right !important;
        margin-top: 2rem !important;
        border-top: 2px solid #000 !important;
        padding-top: 1rem !important;
    }

    .invoice-total .total-amount {
        font-size: 14pt !important;
        font-weight: bold !important;
    }

    /* ===== REPORT STYLES ===== */
    .report-header {
        text-align: center !important;
        margin-bottom: 2rem !important;
    }

    .report-date {
        text-align: right !important;
        font-size: 10pt !important;
        margin-bottom: 1rem !important;
    }

    .report-summary {
        background-color: #f9f9f9 !important;
        border: 1px solid #000 !important;
        padding: 1rem !important;
        margin-bottom: 2rem !important;
    }

    .summary-item {
        display: flex !important;
        justify-content: space-between !important;
        margin-bottom: 0.5rem !important;
    }

    .summary-item:last-child {
        border-top: 1px solid #000 !important;
        padding-top: 0.5rem !important;
        font-weight: bold !important;
    }

    /* ===== FOOTER ===== */
    .print-footer {
        position: fixed !important;
        bottom: 1cm !important;
        left: 0 !important;
        right: 0 !important;
        text-align: center !important;
        font-size: 8pt !important;
        color: #666 !important;
        border-top: 1px solid #ccc !important;
        padding-top: 0.5rem !important;
    }

    /* ===== UTILITIES ===== */
    .text-center { text-align: center !important; }
    .text-right { text-align: right !important; }
    .text-left { text-align: left !important; }
    .font-bold { font-weight: bold !important; }
    .font-italic { font-style: italic !important; }
    .text-uppercase { text-transform: uppercase !important; }
    .text-lowercase { text-transform: lowercase !important; }

    .border-top { border-top: 1px solid #000 !important; }
    .border-bottom { border-bottom: 1px solid #000 !important; }
    .border-left { border-left: 1px solid #000 !important; }
    .border-right { border-right: 1px solid #000 !important; }

    .mb-1 { margin-bottom: 0.25rem !important; }
    .mb-2 { margin-bottom: 0.5rem !important; }
    .mb-3 { margin-bottom: 1rem !important; }
    .mb-4 { margin-bottom: 1.5rem !important; }
    .mb-5 { margin-bottom: 2rem !important; }

    .mt-1 { margin-top: 0.25rem !important; }
    .mt-2 { margin-top: 0.5rem !important; }
    .mt-3 { margin-top: 1rem !important; }
    .mt-4 { margin-top: 1.5rem !important; }
    .mt-5 { margin-top: 2rem !important; }

    .p-1 { padding: 0.25rem !important; }
    .p-2 { padding: 0.5rem !important; }
    .p-3 { padding: 1rem !important; }

    /* ===== SPECIFIC COMPONENTS ===== */
    .order-summary {
        border: 2px solid #000 !important;
        padding: 1rem !important;
        margin: 1rem 0 !important;
    }

    .customer-info {
        background-color: #f9f9f9 !important;
        border: 1px solid #000 !important;
        padding: 1rem !important;
        margin-bottom: 1rem !important;
    }

    .product-list {
        margin: 1rem 0 !important;
    }

    .product-item {
        border-bottom: 1px solid #ccc !important;
        padding: 0.5rem 0 !important;
        display: flex !important;
        justify-content: space-between !important;
    }

    .product-item:last-child {
        border-bottom: none !important;
    }

    /* ===== QR CODE / BARCODE ===== */
    .qr-code,
    .barcode {
        text-align: center !important;
        margin: 1rem 0 !important;
    }

    .qr-code img,
    .barcode img {
        border: none !important;
        max-width: 3cm !important;
        max-height: 3cm !important;
    }
}

/* ===== PRINT PREVIEW STYLES ===== */
.print-preview {
    background: white;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin: 2rem auto;
    max-width: 21cm;
    min-height: 29.7cm;
    padding: 2cm;
    position: relative;
}

.print-preview::before {
    content: 'Print Preview';
    position: absolute;
    top: -2rem;
    left: 0;
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px 4px 0 0;
    font-size: 0.875rem;
    font-weight: 600;
}

/* ===== PRINT BUTTON ===== */
.print-btn {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    background: var(--primary-gradient);
    color: white;
    border: none;
    border-radius: 50px;
    padding: 1rem 2rem;
    font-weight: 600;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    transition: all 0.3s ease;
    z-index: 1000;
}

.print-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.3);
}

.print-btn i {
    margin-right: 0.5rem;
}

@media print {
    .print-btn {
        display: none !important;
    }
}
