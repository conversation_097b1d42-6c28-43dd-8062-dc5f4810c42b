<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'user_id',
        'status',
        'subtotal',
        'tax_amount',
        'shipping_amount',
        'discount_amount',
        'total_amount',
        'coupon_id',
        'coupon_code',
        'coupon_discount',
        'shipping_address',
        'billing_address',
        'shipping_method',
        'shipped_at',
        'delivered_at',
        'tracking_number',
        'payment_status',
        'payment_method',
        'payment_reference',
        'paid_at',
        'notes',
        'admin_notes'
    ];

    protected $casts = [
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'shipping_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'coupon_discount' => 'decimal:2',
        'shipping_address' => 'array',
        'billing_address' => 'array',
        'shipped_at' => 'datetime',
        'delivered_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the user that owns the order
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the coupon used in this order
     */
    public function coupon(): BelongsTo
    {
        return $this->belongsTo(Coupon::class);
    }

    /**
     * Get the order items
     */
    public function items(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the coupon usage for this order
     */
    public function couponUsage(): HasMany
    {
        return $this->hasMany(CouponUsage::class);
    }

    /**
     * Generate unique order number
     */
    public static function generateOrderNumber(): string
    {
        do {
            $orderNumber = 'ORD-' . date('Y') . '-' . strtoupper(Str::random(8));
        } while (self::where('order_number', $orderNumber)->exists());

        return $orderNumber;
    }

    /**
     * Create order from cart
     */
    public static function createFromCart(
        int $userId,
        array $shippingAddress,
        array $billingAddress = null,
        string $paymentMethod = 'cash',
        string $notes = null
    ): self {
        $cartSummary = Cart::getCartSummary($userId);
        
        if (empty($cartSummary['items'])) {
            throw new \Exception('السلة فارغة');
        }

        $billingAddress = $billingAddress ?? $shippingAddress;

        // Create order
        $order = self::create([
            'order_number' => self::generateOrderNumber(),
            'user_id' => $userId,
            'status' => 'pending',
            'subtotal' => $cartSummary['subtotal'],
            'tax_amount' => 0, // Calculate tax if needed
            'shipping_amount' => 0, // Calculate shipping if needed
            'discount_amount' => $cartSummary['total_discount'],
            'total_amount' => $cartSummary['total'],
            'shipping_address' => $shippingAddress,
            'billing_address' => $billingAddress,
            'payment_method' => $paymentMethod,
            'payment_status' => 'pending',
            'notes' => $notes,
        ]);

        // Create order items
        foreach ($cartSummary['items'] as $cartItem) {
            $product = $cartItem->product;
            
            // Get applied discounts
            $appliedDiscounts = [];
            $quantityDiscount = $product->getQuantityDiscount($cartItem->quantity);
            if ($quantityDiscount) {
                $appliedDiscounts[] = [
                    'type' => 'quantity_discount',
                    'description' => $quantityDiscount->description,
                    'amount' => $quantityDiscount->calculateDiscount($cartItem->price, $cartItem->quantity)
                ];
            }

            OrderItem::create([
                'order_id' => $order->id,
                'product_id' => $product->id,
                'product_name' => $product->name,
                'product_sku' => $product->sku,
                'quantity' => $cartItem->quantity,
                'unit_price' => $cartItem->price,
                'unit_discount' => $cartItem->price - $cartItem->final_price,
                'final_unit_price' => $cartItem->final_price,
                'total_price' => $cartItem->total_price,
                'product_options' => $cartItem->product_options,
                'applied_discounts' => $appliedDiscounts,
            ]);

            // Update product quantity
            $product->decrement('quantity', $cartItem->quantity);
        }

        // Clear cart
        Cart::clearCart($userId);

        return $order;
    }

    /**
     * Apply coupon to order
     */
    public function applyCoupon(string $couponCode): bool
    {
        $coupon = Coupon::findByCode($couponCode);
        
        if (!$coupon || !$coupon->isValidForUser($this->user_id)) {
            return false;
        }

        return $coupon->applyToOrder($this, $this->user_id);
    }

    /**
     * Remove coupon from order
     */
    public function removeCoupon(): bool
    {
        if (!$this->coupon_id) {
            return false;
        }

        // Remove coupon usage record
        CouponUsage::where('order_id', $this->id)->delete();

        // Decrement coupon usage count
        if ($this->coupon) {
            $this->coupon->decrement('used_count');
        }

        // Update order
        $this->update([
            'coupon_id' => null,
            'coupon_code' => null,
            'coupon_discount' => 0,
            'discount_amount' => $this->discount_amount - $this->coupon_discount,
            'total_amount' => $this->subtotal + $this->tax_amount + $this->shipping_amount - ($this->discount_amount - $this->coupon_discount),
        ]);

        return true;
    }

    /**
     * Update order status
     */
    public function updateStatus(string $status): bool
    {
        $validStatuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded'];
        
        if (!in_array($status, $validStatuses)) {
            return false;
        }

        $this->status = $status;

        // Set timestamps based on status
        switch ($status) {
            case 'shipped':
                $this->shipped_at = now();
                break;
            case 'delivered':
                $this->delivered_at = now();
                break;
        }

        return $this->save();
    }

    /**
     * Mark order as paid
     */
    public function markAsPaid(string $paymentReference = null): bool
    {
        $this->update([
            'payment_status' => 'paid',
            'payment_reference' => $paymentReference,
            'paid_at' => now(),
        ]);

        return true;
    }

    /**
     * Cancel order
     */
    public function cancel(): bool
    {
        if (in_array($this->status, ['shipped', 'delivered', 'cancelled', 'refunded'])) {
            return false;
        }

        // Restore product quantities
        foreach ($this->items as $item) {
            $item->product->increment('quantity', $item->quantity);
        }

        // Remove coupon usage if any
        if ($this->coupon_id) {
            $this->removeCoupon();
        }

        return $this->updateStatus('cancelled');
    }

    /**
     * Get order status badge class
     */
    public function getStatusBadgeClassAttribute(): string
    {
        return match($this->status) {
            'pending' => 'bg-warning',
            'confirmed' => 'bg-info',
            'processing' => 'bg-primary',
            'shipped' => 'bg-secondary',
            'delivered' => 'bg-success',
            'cancelled' => 'bg-danger',
            'refunded' => 'bg-dark',
            default => 'bg-secondary'
        };
    }

    /**
     * Get order status text
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'processing' => 'قيد المعالجة',
            'shipped' => 'تم الشحن',
            'delivered' => 'تم التسليم',
            'cancelled' => 'ملغي',
            'refunded' => 'مسترد',
            default => 'غير معروف'
        };
    }

    /**
     * Get payment status badge class
     */
    public function getPaymentStatusBadgeClassAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => 'bg-warning',
            'paid' => 'bg-success',
            'failed' => 'bg-danger',
            'refunded' => 'bg-info',
            default => 'bg-secondary'
        };
    }

    /**
     * Get payment status text
     */
    public function getPaymentStatusTextAttribute(): string
    {
        return match($this->payment_status) {
            'pending' => 'في الانتظار',
            'paid' => 'مدفوع',
            'failed' => 'فشل',
            'refunded' => 'مسترد',
            default => 'غير معروف'
        };
    }

    /**
     * Check if order can be cancelled
     */
    public function getCanBeCancelledAttribute(): bool
    {
        return !in_array($this->status, ['shipped', 'delivered', 'cancelled', 'refunded']);
    }

    /**
     * Get total items count
     */
    public function getTotalItemsAttribute(): int
    {
        return $this->items->sum('quantity');
    }

    /**
     * Scope for user orders
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope for status
     */
    public function scopeWithStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for payment status
     */
    public function scopeWithPaymentStatus($query, string $paymentStatus)
    {
        return $query->where('payment_status', $paymentStatus);
    }
}
